.app-offre {
    width: 100%;
    form#views-exposed-form-appel-offre-page-1 {
        margin-bottom: 30px;
        input[checked="checked"] + label {
            color: $white;
        }
        .js-form-type-date {
            position: relative;
            label {
                all: unset;
            }
        }
        .form-radios {
            gap: 10px;
        }
    }
   .card {
    position: relative;
    border-radius: 7px;
        &:before {
            content: "";
            height: 100%;
            position: absolute;
            background: $primary;
            border-radius: 14px 0 0 14px;
            width: 7px;
            height: 100%;
        }
        .card-body {
            display: flex;
            flex-direction: column;
            gap: 20px;
            padding: 30px 40px;
        }
        .btn {
            padding: 5px 10px;
            font-size: 14px;
            border-radius: 14px;
            text-transform: none;
            margin-bottom: 20px;
            &.green {
                background: #e8faea;
                border-color: transparent;
                color: #20C831;
                margin-bottom: 10px;
            }
            &.red {
                background: #f8e7e7;
                border-color: transparent;
                color: #c84a4a;
            } 
            &.blue {
                background: #ebf1fa;
                border-color: transparent;
                color: $primary;
            }
        }
        h3 {
            margin-bottom: 10px;
        }
        div.para {
            display: flex;
            gap: 40px;
            @media(max-width: 480px) {
                flex-direction: column;
                gap: 10px;
            }
            p {
                font-family: $quicksand-regular;
                font-size: 14px;
                margin-bottom: 0;
                html[dir="rtl"] & {
                    font-family: "Cairo", sans-serif;
                    font-weight: 400;
                }
                span {
                    font-family: $quicksand-bold;
                    html[dir="rtl"] & {
                        font-family: "Cairo", sans-serif;
                        font-weight: bold;
                    }
                }
            }
            
        }
        .download {
            align-self: flex-start;
            margin-bottom: 0;
        }
   }
   &.app-offre {
    .card {
        .card-body {
            & > div > div:first-of-type{
                display: flex;
                gap: 10px;
                margin-bottom: 20px;
                .btn {
                    margin-bottom: 0;
                }
            }
        }
       }
   }
}