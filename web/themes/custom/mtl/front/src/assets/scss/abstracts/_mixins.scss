// -----------------------------------------------------------------------------
// This file contains all application-wide Sass mixins.
// -----------------------------------------------------------------------------

// ASPECT RATIO
@mixin aspect-ratio($width, $height) {
  position: relative;
  overflow: hidden;

  &::before {
    display: block;
    content: " ";
    width: 100%;
    padding-top: ($height / $width) * 100%;
  }

  > img,
  > video,
  > figure,
  > .ratio-content {
    position: absolute;
    top: 0;
    left: -9999px;
    right: -9999px;
    bottom: 0;
    min-height: 100%;
    min-width: 100%;
    height: 100%;
    width: 100%;
    margin: auto;
    object-fit: cover;
  }

  > iframe,
  > .ratio-iframe {
    position: absolute;
    top: 0;
    left: -9999px;
    right: -9999px;
    bottom: 0;
    min-height: 100%;
    min-width: 100%;
    height: 100%;
    width: 100%;
    margin: auto;
  }

  > iframe {
    min-height: 200%;
    min-width: 200%;
  }
}

@mixin media-min($min) {
  @media screen and (min-width: #{ $min }) {
    @content;
  }
}

@mixin media-between($min, $max) {
  @media screen and (min-width: #{$min}) and (max-width: #{$max}) {
    @content;
  }
}

@mixin media-max($max) {
  @media screen and (max-width: #{$max}) {
    @content;
  }
}

@function strip-unit($value) {
  @return $value / ($value * 0 + 1);
}


@mixin fluid-type($min-vw, $max-vw, $min-font-size, $max-font-size) {
  $u1: unit($min-vw);
  $u2: unit($max-vw);
  $u3: unit($min-font-size);
  $u4: unit($max-font-size);

  @if $u1==$u2 and $u1==$u3 and $u1==$u4 {
    & {
      font-size: $min-font-size;

      @media screen and (min-width: $min-vw) {
        font-size: calc(#{$min-font-size} + #{strip-unit($max-font-size - $min-font-size)} * ((100vw - #{$min-vw}) / #{strip-unit($max-vw - $min-vw)}));
      }

      @media screen and (min-width: $max-vw) {
        font-size: $max-font-size;
      }
    }
  }
}

@mixin header-footer-mobile {
    grid-template-columns: repeat(1, 1fr);
    gap: 0;
    a {
        &:after {
            display: none;
        }
    }
    & > li {
        margin-bottom: 0;
        background-color: rgba($white, .1);
        border-radius: rem(10);
        & > a {
            position: relative;
            display: block;
            height: auto !important;
            font-size: rem(16);
            pointer-events:all !important;
            padding: rem(20) rem(15);
            color: $white;
            margin-bottom: 0;
            &:before {
                content: "\e903";
                font-family: 'icomoon';
                position: absolute;
                // right: rem(15);
                @include end(position, 15px);
                top: rem(10);
                font-size: rem(8);
                color: $white;
                top: 50%;
                transform: translateY(-50%);
                transition: transform 350ms ease;
            }
            &.active {
                color: $white;
                &:before {
                    transform: translateY(-50%) rotateZ(180deg);
                }
            }
            &:hover {
                color: $white;
            }
        }
        & > ul {
            display: none;
            padding: 0 rem(30) rem(30);
        }
    }
}

@mixin swiper-dots {
  .swiper-pagination {
    .swiper-pagination-bullet {
      width: rem(24);
      height: rem(24);
      background: transparent;
      border-color: $white;
      border-style: solid;
      border-width: 1px;
      opacity: 1;

      &-active {
        background: $white;
      }
    }
    &.no-dotes {
      .swiper-pagination-bullet {
        @media(max-width: 600px) {
          display: none;
        }
      }
    }
    &.blue {
      .swiper-pagination-bullet {
        border-color: $primary;
        &-active {
          background: $primary;
        }

      }
    }
    &.dotes-mobile {
      .swiper-pagination-bullet {
        @media(min-width: 601px) {
          display: none;
        }
      }
    }
  }
}

@mixin container-slide {
  .container {
    @media(max-width: 599px) {
      padding-right: 0;
      [dir="rtl"] & {
        padding-right: 15px;
        padding-left: 0;
      }
    }
  }
}