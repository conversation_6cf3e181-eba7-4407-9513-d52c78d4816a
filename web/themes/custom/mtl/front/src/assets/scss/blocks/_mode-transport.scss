.wrapper-trans, .listing-projet {
    width: 100%;
    margin-bottom: 60px;
    @media(max-width:767px) {
        .container{
          max-width: 100%;
        }
    }
    p {
        font-size: rem(18);
    }
    P.d-flex + p {
        display: flex;
        justify-content: center;
    }
    .swiper-grid-column>.swiper-wrapper {
        flex-direction: row;
        align-items: flex-end;
    }
    &__items, .items {
        width: 100%;
        // padding-top: rem(30);
        padding-top: 10px;
        justify-content: center;
        &--item, .item {
            position: relative;
            cursor: pointer;
            .picture {
                position: relative;
                border-radius: 14px;
                box-shadow: 0 10px 12px rgba(67, 101, 151, 0);
                img {
                    width: 100%;
                    transform: scale(1) translateX(0);
                    transition: transform 1.5s ease;
                    border-radius: 14px;
                    height: 100%;
                }
                &::before {
                    content: "";
                    position: absolute;
                    inset: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(#000, .5);
                    z-index: 1;
                    border-radius: 14px;
                }
            }
            .content {
                position: absolute;
                width: auto;
                bottom: 36px;
                @include start(padding, rem(40));
                @include end(margin, rem(10));
                transition: all 1000ms cubic-bezier(0.18, 0.89, 0.32, 1.28);
                z-index: 2;
                overflow: hidden;
                cursor: pointer;
                & > div {
                    overflow: hidden;
                    height: 0;
                    a {
                        position: relative;
                        z-index: 99;
                    }
                }

                h3 {
                    width: 80%;
                    @media(max-width: 1024px) {
                        width: 100%;
                    }
                }
                p {
                    padding-bottom: rem(20);
                    color: $white;
                }
                @media (max-width:768px) {
                    height: auto;
                    bottom: 10%;
                    overflow: visible;
                    transform: translateY(0%);
                    zoom: .9;
                    & > div {
                        overflow: visible;
                        height: auto;
                    }
                }
                @media (max-width:480px) {
                    zoom: .8;
                }
                @media (max-width:320px) {
                    zoom: .7;
                }
            }
            &:hover {
                .content {
                    height: auto;
                    bottom: 10%;
                    overflow: visible;
                    transform: translateY(0%);
                    & > div {
                        overflow: visible;
                        height: auto;
                    }
                }
            }


        }
        
    }
    @include container-slide;
    &.listing-projet {
        form#views-exposed-form-projects-page-1 {
            padding: 30px;
            input[checked="checked"] + label {
                color: $white;
            }
            .js-form-type-select {
                position: relative;
                // &:after {
                //     content: "\e901";
                //     font-family: 'icomoon';
                //     position: absolute;
                //     right: 17px;
                //     bottom: 14px;
                //     font-size: 18px;
                //     color: $primary;
                // } 
                .selectric {
                    position: relative;
                    &:before {
                        content: "";
                        position: absolute;
                        bottom: 2px;
                        right: 3px;
                        background: #ebf1fa;
                        width: 45px;
                        height: 45px;
                        border-radius: 100%;
                    }
                    .button {
                        right: 12px;
                        &:after {
                            color: $primary;
                            font-weight: bold;
                            font-size: 10px;
                        }
                    }

                }
                
                label {
                    all: unset;
                    display: block;
                    padding-bottom: 10px;
                }
            }
            @media(max-width: 600px) {
                fieldset[id^="edit-field-secteur-target-id--wrapper"] {
                  grid-row: 2;
                }
            }
        }
        .items {
            .item {
                .picture {
                    height: 500px;
                    max-height: 500px;
                    img {
                       height: 100%;
                    }
                    &::before {
                        content: "";
                        position: absolute;
                        inset: 0;
                        width: 100%;
                        height: 100%;
                        background: rgba(#000, .4);
                        z-index: 1;
                        border-radius: 14px;
                    }
                }
    
            }
            
        }
    }
}
.no-content {
    display: flex;
    padding: 20px 0 0;
    // justify-content: center;
    p {
        font-family: $rubik-bold;
        color: $primary;
        font-size: 18px;
        // animation: fadeInOut 1.5s ease-out infinite;
    }
}

/* Animation */
@keyframes fadeInOut {
  0%, 100% {
    opacity: 0;
    transform: translateY(20px);
  }
  50% {
    opacity: 1;
    transform: translateY(0);
  }
}