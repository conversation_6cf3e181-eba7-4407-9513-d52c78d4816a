/**
 * Basic typography style for copy text
 A solution for this problem is percentage. Usually default font-size of the browser is 16px.
 Setting font-size: 100% will make 1rem = 16px. But it will make calculations a little difficult.
 A better way is to set font-size: 62.5%. Because 62.5% of 16px is 10px. Which makes 1rem = 10px.
 CALCULATION: Element font size in rem x 16px;
 */

 // Body text
// -------------------------

html {
  // font-size: 62.5%;
  overflow-x: hidden;
  scroll-behavior: smooth;
}

body {
  position: relative;
  font-size: 0.875rem;
  font-weight: $font-weight-normal;
  font-family: $rubik-regular;
  letter-spacing: 0.4px;
  line-height: 1.5rem;
  color: $black;
  background: $colorBody;

  @include media-breakpoint-up(lg) {
    @include font-size(1rem);
  }
  html[dir="rtl"] & {
    font-family: "Cairo", sans-serif;
  }

}
ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

h1,
h2,
h3,
h4,
h5,
h6,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
  margin-bottom: 28px;

  @include media-breakpoint-up(lg) {
    margin-bottom: 36px;
  }
}

ol,
ul,
p,
blockquote,
.preamble {
  margin-bottom: 28px;

  @include media-breakpoint-up(lg) {
    margin-bottom: 36px;
  }
}

// Headings
// -------------------------
h1,
h2,
h3,
h4,
.h1,
.h2,
.h3,
.h4 {
  // font-family: $font-family-playfair;
}

h5,
h6,
.h5,
.h6 {
  // font-family: $font-family-heebo;
}

h1,
.h1 {
  color: $secondary;
  font-size: $h1-font-size-touch;
  font-weight: $font-weight-normal;
  line-height: $h1-line-height-touch;
  letter-spacing: 0.13px;
  text-transform: uppercase;

  @include media-breakpoint-up(lg) {
    @include font-size($h1-font-size);
    letter-spacing: 0.22px;
    line-height: $h1-line-height;
  }
}

h2,
.h2 {
  color: $secondary;
  font-size: $h2-font-size-touch;
  font-weight: $font-weight-normal;
  letter-spacing: 0.08px;
  line-height: $h2-line-height-touch;
  text-transform: initial;

  @include media-breakpoint-up(lg) {
    @include font-size($h2-font-size);
    letter-spacing: 0.14px;
    line-height: $h2-line-height;
  }
}

h3,
.h3 {
  color: $secondary;
  font-size: $h3-font-size-touch;
  font-weight: $font-weight-normal;
  letter-spacing: 0.06px;
  line-height: $h3-line-height-touch;
  text-transform: initial;

  @include media-breakpoint-up(lg) {
    @include font-size($h3-font-size);
    letter-spacing: 0.08px;
    line-height: $h3-line-height;
  }
}

h4,
.h4 {
  color: $secondary;
  font-size: $h4-font-size-touch;
  font-weight: $font-weight-normal;
  letter-spacing: 0.05px;
  line-height: $h4-line-height-touch;
  text-transform: uppercase;

  @include media-breakpoint-up(lg) {
    @include font-size($h4-font-size);
    line-height: $h4-line-height;
  }
}

h5,
.h5 {
  color: $secondary;
  font-size: $h5-font-size-touch;
  font-weight: $font-weight-bold;
  letter-spacing: normal;
  line-height: $h5-line-height-touch;
  text-transform: initial;

  @include media-breakpoint-up(lg) {
    @include font-size($h5-font-size);
    line-height: $h5-line-height;
  }
}

h6,
.h6 {
  color: $secondary;
  font-size: $h6-font-size-touch;
  font-weight: $font-weight-bold;
  letter-spacing: normal;
  line-height: $h6-line-height-touch;
  text-transform: initial;

  @include media-breakpoint-up(lg) {
    @include font-size($h6-font-size);
    line-height: $h6-line-height;
  }
}

a {
  color: $black;
  &:hover {
    color: $black;
    text-decoration: none;
  }
}
.h1-title {
  font-family: $rubik-bold;
  font-weight: bold;
  // font-size: 35px;
  font-size: 30px;
  text-transform: none;
  margin-bottom: 0;
  color: $white;
  line-height: normal;
  text-shadow: 3px 3px 4px #000;
  &::first-letter {
    text-transform: uppercase;
  }
   html[dir="rtl"] &::first-letter {
    text-transform: none;
  }
  html[dir="rtl"] & {
    font-family: "Cairo", sans-serif;
  }
  @media(max-width:600px) {
    font-size: 24px;
  }
}
:lang(ar) .h1-title::first-letter {
  text-transform: none;
}
.h3-title, h3 {
  font-family: $rubik-bold;
  font-weight: bold;
  font-size: rem(20);
  color: $white;
  line-height: 1.4;
  margin-bottom: 0;
  html[dir="rtl"] & {
    font-family: "Cairo", sans-serif;
  }
  // text-transform: uppercase;
  &.black {
    color: $black;
  }
  &.lowercase {
    text-transform: none;
  }
  @media(max-width:600px) {
    font-size: 16px;
  }
}

p {
  font-family: $quicksand-regular;
  font-size: rem(16);
  line-height: 1.4;
  html[dir="rtl"] & {
      font-family: "Cairo", sans-serif;
      font-weight: 400;
  }
  margin: 0;
  &.white {
    color: $white;
  }
}
.card {
  span.date, p.date {
    position: relative;
    font-family: $quicksand-regular;
    font-size: clamp(16px, 4vw, 17px);
    display: block;
    // padding-left: rem(20);
    @include start(padding, rem(20));
    html[dir="rtl"] & {
      font-family: "Cairo", sans-serif;
      font-weight: 400;
    }
    &:after {
      content: "\e901";
      font-family: 'icomoon';
      position: absolute;
      font-size: rem(16);
      color: #6B6B6B;
      // left: 0;
      @include start(position, 0);
      top: 0;
    }
  }
  span.localisation {
    padding-left: rem(15);
    a {
      color: #6B6B6B;
    }
    &:after {
      content: "\e90a";
    }
  }
  .card-body {
    p.para-bold {
      font-family: $rubik-bold;
      font-weight: bold;
      font-size: clamp(16px, 4vw, 18px);
      margin-bottom: 0;
      html[dir="rtl"] & {
        font-family: "Cairo", sans-serif;
      }
    }
  }
}

.h2-title {
  font-family: $rubik-bold;
  font-size: rem(30);
  color: $primary;
  display: block;
  line-height: 22px;
  margin-bottom: 24px;
  line-height: 1.1;
  &::first-letter {
    text-transform: uppercase;
  }
  html[dir="rtl"] &::first-letter {
    text-transform: none;
  }
  html[dir="rtl"] & {
    font-family: "Cairo", sans-serif;
    font-weight: 700;
  }
 
  &.white {
    color: $white;
  }
  &.black {
    color: $black;
  }
  &.transform-none {
    text-transform: none;
  }
  &.title-reg {
    font-size: 20px;
    color: $black;
    line-height: 1.5;
    margin-bottom: 8px;
    // display: -webkit-box;
    // -webkit-line-clamp: 2;
    // -webkit-box-orient: vertical;
    // overflow: hidden;
    // text-overflow: ellipsis;
  }
  @media only screen and (max-width: 768px) {
    font-size: rem(22);
    &.title-reg {
      font-size: 18px;
    }
  }
  &.h3-title {
    font-size: rem(20);
    margin-bottom: 10px;
    text-transform: none;
    @media only screen and (max-width: 768px) {
      font-size: rem(18);
    }
    &.transform-none {
      text-transform: none;
    }
    // #first-letter {
    //   position: relative;
    //   bottom: 0;
    //   text-transform: uppercase;
    //   font-size: inherit;
    //   color: inherit;
    //   font-family: inherit;
    // }
  }
  &.h4-title {
    font-size: rem(20);
    @media only screen and (max-width: 768px) {
      font-size: rem(18);
    }
  }
  @media only screen and (max-width: 600px) {
    justify-content: flex-start !important;
  }
}
*,
*::before,
*::after {
  box-sizing: border-box;
}