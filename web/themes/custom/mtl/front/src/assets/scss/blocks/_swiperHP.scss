.sliderHeader {
    width: 100%;
    .container {
        position: relative;
        @media(max-width:767px) {
            max-width: 100%;
        }
    }
    &__items {
        max-width: rem(800);
        margin-right: initial;
        position: absolute;
        bottom: 20px;
        right: 15px;
        left: 15px;
        z-index: 3;
        // @media(max-width: 599px) {
        //     right: 0;
        // }
    }
    .slide-content {
        .picture {
            margin-bottom: rem(15);
            border-radius: 14px;
            background: $white;
            padding: 2px;
            overflow: hidden;
            transition: all 450ms ease;
            img {
                // width: auto;
                width: 100%;
                max-width: 100%;
                border-radius: 14px;
                transform: scale(1);
                transition: all 450ms ease;
            }
        }
        .content {
            p {
                font-size: rem(13);
                line-height: 1.3;
                color: $white;
            }
            
        }
        &:hover {
            .picture {
                box-shadow: 0 10px 12px rgba(67, 101, 151, 0.27);
                img {
                    transform: scale(1.2);
                }
            }
        }
    }
    
}