<div class="card">
  <!-- Icon style 46 * 46 -->
  {{ fields.field_icon.content|replace({ '<img ': '<img class="card-img-top" ' })|raw }}
  <div class="card-body">
    <div>
      <span class="date">{{ fields.field_date.content }}</span>
      {% set link = fields.field_secteur.content %}
      {{ link|replace({ '<a ': '<a class="btn btn-light" ' })|raw }}
    </div>
    {{ dump(row._entity.field_titre_long.value) }}
    {{ dump(fields.title.content) }}
    {% if row._entity.field_titre_long.value %}
      <h2 class="h2-title title-reg">{{ fields.field_titre_long.content|striptags|trim|replace({ '&#039;': "'" }) }}</h2>
    {% else %}
      <h2 class="h2-title title-reg">{{ fields.title.content|striptags|trim|replace({ '&#039;': "'" }) }}</h2>
    {% endif %}

    {% if row._entity.field_lien_telechargement.entity.uri.value %}
      <a href="{{ file_url(row._entity.field_lien_telechargement.entity.uri.value) }}" class="btn btn-success bold download" target="_blank"><i class="fa-solid fa-download"></i>{{ 'Télécharger'|trans }}</a>
    {% endif %}
  </div>
</div>
