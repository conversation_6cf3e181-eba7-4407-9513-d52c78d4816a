{#
/**
 * @file
 * Default theme implementation for main view template.
 *
 * Available variables:
 * - attributes: Remaining HTML attributes for the element.
 * - css_name: A CSS-safe version of the view name.
 * - css_class: The user-specified classes names, if any.
 * - header: The optional header.
 * - footer: The optional footer.
 * - rows: The results of the view query, if any.
 * - empty: The content to display if there are no rows.
 * - pager: The optional pager next/prev links to display.
 * - exposed: Exposed widget form/info to display.
 * - feed_icons: Optional feed icons to display.
 * - more: An optional link to the next page of results.
 * - title: Title of the view, only used when displaying in the admin preview.
 * - title_prefix: Additional output populated by modules, intended to be
 *   displayed in front of the view title.
 * - title_suffix: Additional output populated by modules, intended to be
 *   displayed after the view title.
 * - attachment_before: An optional attachment view to be displayed before the
 *   view content.
 * - attachment_after: An optional attachment view to be displayed after the
 *   view content.
 * - dom_id: Unique id for every view being printed to give unique class for
 *   JavaScript.
 *
 * @see template_preprocess_views_view()
 *
 * @ingroup themeable
 */
#}
{% set classes = [dom_id ? 'js-view-dom-id-' ~ dom_id] %}
{% set current_language = drupal_config('system.site', 'default_langcode') %}
{% set current_url_language = language.getId() %}
<div {{ attributes.addClass(classes) }}>
  <section class="presse app-offre carr-prof margeBlock">
    <div class="container">
      <div class="row g-3">
        <div class="col-12">
          {{ title_prefix }}
          {{ title }}
          {{ title_suffix }}

          {% if header %}
            <header>{{ header }}</header>
          {% endif %}

          {{ exposed }}
        </div>
        {{ attachment_before }}

        {% if rows -%}
          {{ rows }}
        {% elseif empty -%}
          <div class="container no-content">
            <p><p>{{ 'Aucun résultat trouvé'|t }}</p</p>
          </div>
        {% endif %}
        {{ pager }}

        {{ attachment_after }}
         <div class="btn-wrapper d-flex justify-content-center mt-4">
         {% if current_url_language == 'fr' %}
          <a class="btn btn-success bold" href="/recrutement-et-carrieres/archives">
            {{ 'Voir les archives'|t }}
            <i class="fa-solid fa-arrow-right"></i>
          </a>
         {% elseif current_url_language == 'ar' %}
          <a class="btn btn-success bold" href="/ar/recrutement-et-carrieres/archives">
            {{ 'عرض الأرشيف'|t }}
            <i class="fa-solid fa-arrow-left"></i>
          </a>
         {% else %}
          <a class="btn btn-success bold" href="/recrutement-et-carrieres/archives">
            {{ 'Voir les archives'|t }}
            <i class="fa-solid fa-arrow-right"></i>
          </a>
         {% endif %}
        </div>
        {# {{ more }} #}

        {% if footer %}
          <footer>{{ footer }}</footer>
        {% endif %}

        {{ feed_icons }}
      </div>
    </div>
  </section>
  {{ drupal_entity('block', 'mtl_decouvriraussimediatheque') }}
</div>
