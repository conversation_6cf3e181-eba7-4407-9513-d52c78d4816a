{#
/**
 * @file
 * Default view template to display all the fields in a row.
 *
 * Available variables:
 * - view: The view in use.
 * - fields: A list of fields, each one contains:
 *   - content: The output of the field.
 *   - raw: The raw data for the field, if it exists. This is NOT output safe.
 *   - class: The safe class ID to use.
 *   - handler: The Views field handler controlling this field.
 *   - inline: Whether or not the field should be inline.
 *   - wrapper_element: An HTML element for a wrapper.
 *   - wrapper_attributes: List of attributes for wrapper element.
 *   - separator: An optional separator that may appear before a field.
 *   - label: The field's label text.
 *   - label_element: An HTML element for a label wrapper.
 *   - label_attributes: List of attributes for label wrapper.
 *   - label_suffix: Colon after the label.
 *   - element_type: An HTML element for the field content.
 *   - element_attributes: List of attributes for HTML element for field content.
 *   - has_label_colon: A boolean indicating whether to display a colon after
 *     the label.
 *   - element_type: An HTML element for the field content.
 *   - element_attributes: List of attributes for HTML element for field content.
 * - row: The raw result from the query, with all data it fetched.
 */
#}


<div class="card">
	<div class="card-body">
		<div>
			<div>
			{% if row._entity.field_date_limit is not empty %}

                    {% set now = "now"|date("U") %}


                    {% if now > date_limit %}
                        <a type="button" class="btn btn-success green red">{{ "Fermé" | t }}</a>

                    {% else %}
                        <a type="button" class="btn btn-success green">{{ "Ouvert" | t }}</a>

                    {% endif %}
                {% endif %}
				<a type="button" class="btn btn-primary blue">{{ fields.field_type_carrieres.content }}</a>
			</div>
			<div>
				<h3>{{ fields.title.content }}</h3>
			</div>
			<div class="para">
				<p class="date">
					<span>{{ "Date de publication:" | t }}</span>
					{{ fields.field_date.content }}</p>
				<p class="date">
					<span>{{ "Date limite de dépôt:" | t }}</span>
					{{ fields.field_date_limit.content }}</p>
			</div>
		</div>
		<div class="btn-carriere d-flex flex-wrap gap-2">

		
		{% if row._entity.field_document is not empty %}
			<a href="{{ file_url(row._entity.field_document.entity.uri.value) }}" class="btn btn-success bold download" target="_blank">
				<i class="fa-solid fa-download"></i>
				{% if row._entity.field_document.0.description is not empty %}
					{{ row._entity.field_document.0.description }}</a>
				{% else %}
					{{"Avis" | t}}</a>
				{% endif %}
		{% endif %}
		{% if row._entity.field_liste_des_convoques is not empty %}
			<a href="{{ file_url(row._entity.field_liste_des_convoques.entity.uri.value) }}" class="btn btn-success bold download" target="_blank">
				<i class="fa-solid fa-download"></i>
				{{ "Liste des convoqués" | t }}</a>
		{% endif %}
		{% if row._entity.field_resultat is not empty %}
		{% for item in row._entity.field_resultat.entity.uri %}
			
			<a href="{{ file_url(item.value) }}" class="btn btn-success bold download" target="_blank">
				<i class="fa-solid fa-download"></i>
				{{ "Résultats" | t }}</a>
		{% endfor %}
		{% endif %}
		</div>


	</div>
</div>
