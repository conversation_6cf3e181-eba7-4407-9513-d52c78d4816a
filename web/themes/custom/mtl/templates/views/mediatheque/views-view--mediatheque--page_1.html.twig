{#
/**
 * @file
 * Default theme implementation for main view template.
 *
 * Available variables:
 * - attributes: Remaining HTML attributes for the element.
 * - css_name: A CSS-safe version of the view name.
 * - css_class: The user-specified classes names, if any.
 * - header: The optional header.
 * - footer: The optional footer.
 * - rows: The results of the view query, if any.
 * - empty: The content to display if there are no rows.
 * - pager: The optional pager next/prev links to display.
 * - exposed: Exposed widget form/info to display.
 * - feed_icons: Optional feed icons to display.
 * - more: An optional link to the next page of results.
 * - title: Title of the view, only used when displaying in the admin preview.
 * - title_prefix: Additional output populated by modules, intended to be
 *   displayed in front of the view title.
 * - title_suffix: Additional output populated by modules, intended to be
 *   displayed after the view title.
 * - attachment_before: An optional attachment view to be displayed before the
 *   view content.
 * - attachment_after: An optional attachment view to be displayed after the
 *   view content.
 * - dom_id: Unique id for every view being printed to give unique class for
 *   JavaScript.
 *
 * @see template_preprocess_views_view()
 *
 * @ingroup themeable
 */
#}
{% set classes = [dom_id ? 'js-view-dom-id-' ~ dom_id] %}

<section class="mediatheque margeBlock">
  <div class="container">
    {{ exposed }}

    <div class="row g-3">
      {% if rows -%}
        {{ rows }}
      {% elseif empty -%}
        <div class="container no-content">
          <p><p>{{ 'Aucun résultat trouvé'|t }}</p</p>
        </div>
      {% endif %}
    </div>
  </div>
</section>
<div class="video-modal">
  <div id="video-modal-content" class="video-modal-content">
    <iframe id="youtube" width="100%" height="100%" frameborder="0" allow="autoplay" allowfullscreen src=""></iframe>
    <span href="#" class="close-video-modal">X</span>
  </div>
  <div class="overlay"></div>
</div>
