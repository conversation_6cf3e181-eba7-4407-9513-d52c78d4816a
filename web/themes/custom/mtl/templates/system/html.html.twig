{#
/**
 * @file
 * Default theme implementation for the basic structure of a single Drupal page.
 *
 * Variables:
 * - logged_in: A flag indicating if user is logged in.
 * - root_path: The root path of the current page (e.g., node, admin, user).
 * - node_type: The content type for the current node, if the page is a node.
 * - head_title: List of text elements that make up the head_title variable.
 *   May contain one or more of the following:
 *   - title: The title of the page.
 *   - name: The name of the site.
 *   - slogan: The slogan of the site.
 * - page_top: Initial rendered markup. This should be printed before 'page'.
 * - page: The rendered page markup.
 * - page_bottom: Closing rendered markup. This variable should be printed after
 *   'page'.
 * - db_offline: A flag indicating if the database is offline.
 * - placeholder_token: The token for generating head, css, js and js-bottom
 *   placeholders.
 *
 * @see template_preprocess_html()
 *
 * @ingroup themeable
 */
#}
<!DOCTYPE html>
<html{{html_attributes}}>
	<head>
		<link rel="preconnect" href="https://fonts.googleapis.com">
		<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
		<head-placeholder token="{{ placeholder_token }}">
			<title>{{ head_title|safe_join(' | ') }}</title>
			<css-placeholder token="{{ placeholder_token }}">
				<js-placeholder token="{{ placeholder_token }}"></head>
				<body{{attributes}}>
					{#
					      Keyboard navigation/accessibility link to main content section in
					      page.html.twig.
					    #}
					<a href="#main-content" class="visually-hidden focusable">
						{{ 'Skip to main content'|t }}
					</a>
					<!-- Accessibilty Module -->
					<div id="accessPanel" class="accessibility-panel" style="display: none;">
						<div class="accessPanel-header">
							<div class="accessPanel-header__l">
								<h5 class="accessPanel-header__title">Visual Accessibility Options</h5>
							</div>
							<button class="accessPanel-header__close" type="button" aria-label="Close the accessibility menu">
								<span style="width: 24px; height: 16px; display: flex;">
									<svg xmlns="http://www.w3.org/2000/svg" viewbox="0 0 14 14" width="100%" height="100%">
										<path fill="none" stroke="currentColor" stroke-linecap="round" stroke-width="2" d="m2.30436241 2.30436238 9.39127515 9.39127515m-9.39127518 6e-8 9.39127515-9.39127515"></path>
									</svg>
								</span>
								<span class="focus-outline"></span>
							</button>
						</div>
						<div class="accessPanel-features__wrapper">
							<button class="accessPanel-features__reset">
								<span class="accessPanel-features__reset__i">
									<svg xmlns="http://www.w3.org/2000/svg" viewbox="0 0 19 16" width="100%" height="100%">
										<g fill="none" fill-rule="evenodd" stroke="currentColor" stroke-linecap="round" stroke-width="1.84">
											<path d="M16.20106 8c0 .9667-.189683 1.8872-.5324673 2.7251-.3427843.8372-.8386698 1.5911-1.4517524 2.2246-.6130825.6335-1.3426846 1.1459-2.152902 1.5001-.8108948.3542-1.70172746.5502-2.6372711.5502-.93554365 0-1.8263763-.196-2.63727112-.5502-.81021738-.3542-1.53981948-.8666-2.15290203-1.5001M2.6522744 8c0-.9667.189683-1.8872.53246728-2.7251.34278427-.8372.83866982-1.5911 1.45175237-2.2246.61308255-.6335 1.34268465-1.1459 2.15290203-1.5001C7.6002909 1.196 8.49112355 1 9.4266672 1c.93554364 0 1.8263763.196 2.6372711.5502.8102174.3542 1.5398195.8666 2.152902 1.5001"></path>
											<path stroke-linejoin="round" d="m4.92576062 6.96092-2.48958935 1.484L1 5.87242m13.0125924 2.93832 2.3886509-1.652L18 9.62694"></path>
										</g>
									</svg>
								</span>
								<span class="accessPanel-features__reset__text">Reset All Accessibility
									            Settings</span>
							</button>
							<div class="accessPanel-features__title">
								<span id="content_title" class="accessPanel-features__title__s">Content adjustements</span>
							</div>
							<div class="accessPanel-features">
								<div class="accessPanel-features__item">
									<button type="button" id="spacingText" class="accessPanel-features__item__i">
										<span class="accessPanel-features__item__icon">
											<span class="icon icon-spacing-1"></span>
										</span>
										<div class="accessPanel-features__item__name">Text Spacing</div>
										<span class="accessPanel-features__item__steps">
											<span class="accessPanel-features__step">
												<span class="accessPanel-features__step__i"></span>
											</span>
											<span class="accessPanel-features__step">
												<span class="accessPanel-features__step__i"></span>
											</span>
											<span class="accessPanel-features__step">
												<span class="accessPanel-features__step__i"></span>
											</span>
										</span>
									</button>
								</div>
								<div class="accessPanel-features__item">
									<button type="button" id="biggerText" class="accessPanel-features__item__i">
										<span class="accessPanel-features__item__icon">
											<span class="icon icon-bigger-text-1"></span>
										</span>
										<div class="accessPanel-features__item__name">Bigger Text</div>
										<span class="accessPanel-features__item__steps">
											<span class="accessPanel-features__step">
												<span class="accessPanel-features__step__i"></span>
											</span>
											<span class="accessPanel-features__step">
												<span class="accessPanel-features__step__i"></span>
											</span>
											<span class="accessPanel-features__step">
												<span class="accessPanel-features__step__i"></span>
											</span>
											<span class="accessPanel-features__step">
												<span class="accessPanel-features__step__i"></span>
											</span>
										</span>
									</button>
								</div>
								<div class="accessPanel-features__item">
									<button type="button" id="lineHeightText" class="accessPanel-features__item__i">
										<span class="accessPanel-features__item__icon">
											<span class="icon icon-line-height-1"></span>
										</span>
										<div class="accessPanel-features__item__name">Line Height</div>
										<span class="accessPanel-features__item__steps">
											<span class="accessPanel-features__step">
												<span class="accessPanel-features__step__i"></span>
											</span>
											<span class="accessPanel-features__step">
												<span class="accessPanel-features__step__i"></span>
											</span>
											<span class="accessPanel-features__step">
												<span class="accessPanel-features__step__i"></span>
											</span>
										</span>
									</button>
								</div>
								<div class="accessPanel-features__item">
									<button type="button" id="dyslexiaText" class="accessPanel-features__item__i">
										<span class="accessPanel-features__item__icon">
											<span class="icon icon-font-dx"></span>
										</span>
										<div class="accessPanel-features__item__name">Dyslexia Friendly</div>
										<span class="accessPanel-features__item__steps">
											<span class="accessPanel-features__step">
												<span class="accessPanel-features__step__i"></span>
											</span>
											<span class="accessPanel-features__step">
												<span class="accessPanel-features__step__i"></span>
											</span>
										</span>
									</button>
								</div>
							</div>
							<div class="accessPanel-features__title">
								<span id="display_title" class="accessPanel-features__title__s">Display adjustements</span>
							</div>
							<div class="accessPanel-features">
								<div class="accessPanel-features__item">
									<button type="button" id="contrastBg" class="accessPanel-features__item__i">
										<span class="accessPanel-features__item__icon">
											<span class="icon icon-contrast-plus"></span>
										</span>
										<div class="accessPanel-features__item__name">Contrast +</div>
										<span class="accessPanel-features__item__steps">
											<span class="accessPanel-features__step">
												<span class="accessPanel-features__step__i"></span>
											</span>
											<span class="accessPanel-features__step">
												<span class="accessPanel-features__step__i"></span>
											</span>
										</span>
									</button>
								</div>
								<div class="accessPanel-features__item">
									<button type="button" id="highlightLinks" class="accessPanel-features__item__i">
										<span class="accessPanel-features__item__icon">
											<span class="icon icon-highlight-links"></span>
										</span>
										<div class="accessPanel-features__item__name">Highlight Links</div>
									</button>
								</div>
								<div class="accessPanel-features__item">
									<button type="button" id="cursorGuide" class="accessPanel-features__item__i">
										<span class="accessPanel-features__item__icon">
											<span class="icon icon-cursor-1"></span>
										</span>
										<div class="accessPanel-features__item__name">Cursor</div>
										<span class="accessPanel-features__item__steps">
											<span class="accessPanel-features__step">
												<span class="accessPanel-features__step__i"></span>
											</span>
											<span class="accessPanel-features__step">
												<span class="accessPanel-features__step__i"></span>
											</span>
											<span class="accessPanel-features__step">
												<span class="accessPanel-features__step__i"></span>
											</span>
										</span>
									</button>
								</div>
								<div class="accessPanel-features__item">
									<button type="button" id="hideImages" class="accessPanel-features__item__i">
										<span class="accessPanel-features__item__icon">
											<span class="icon icon-hide-images"></span>
										</span>
										<div class="accessPanel-features__item__name">Hide Images</div>
									</button>
								</div>
								<div class="accessPanel-features__item">
									<button type="button" id="saturationBg" class="accessPanel-features__item__i">
										<span class="accessPanel-features__item__icon">
											<span class="icon icon-saturation-1"></span>
										</span>
										<div class="accessPanel-features__item__name">Saturation</div>
										<span class="accessPanel-features__item__steps">
											<span class="accessPanel-features__step">
												<span class="accessPanel-features__step__i"></span>
											</span>
											<span class="accessPanel-features__step">
												<span class="accessPanel-features__step__i"></span>
											</span>
											<span class="accessPanel-features__step">
												<span class="accessPanel-features__step__i"></span>
											</span>
										</span>
									</button>
								</div>
							</div>
						</div>
						<div class="accessPanel-footer">
							<div class="accessPanel-footer__l">
								<h5 class="accessPanel-footer__title">Spark Vibe ForNet</h5>
							</div>
						</div>
					</div>
					<!-- ./ Accessibilty Module -->
					{{ page_top }}
					{{ page }}
					{{ page_bottom }}
					<div class="wrapper-accessibilty">
						<button aria-label="Name" id="toggleAccessPanel" type="button" class="btn-accessibilty toggel-panel-edtior btn btn-sm">
							<svg xmlns="http://www.w3.org/2000/svg" viewbox="0 0 63 63" style="">
								<defs>
									<style>
										.a,
										.b {
											fill: #fff;
										}

										.b {
											opacity: 0.85;
										}
									</style>
								</defs>
								<title>{{ "Accessibilité"|t }}</title>
								<circle class="a" cx="31.49" cy="16.08" r="5.5" transform="translate(-2.14 26.98) rotate(-45)"></circle>
								<path class="a" d="M47.62,22.28a88.73,88.73,0,0,1-32.25,0,1.63,1.63,0,0,0-1.92,1.32,1.64,1.64,0,0,0,1.32,1.92,92,92,0,0,0,10.92,1.34,2.62,2.62,0,0,1,2.42,2.93l-.32,2.75a54.14,54.14,0,0,1-3,12.67l-2,5.35a1.65,1.65,0,0,0,3.09,1.16v0l.49-1c1.6-3.41,3.09-6.9,4.45-10.43a.74.74,0,0,1,1.38,0c1.36,3.53,2.85,7,4.45,10.44l.51,1.09h0a1.64,1.64,0,0,0,1.48,1,1.6,1.6,0,0,0,.57-.11,1.64,1.64,0,0,0,1-2.12l-2-5.35a54.67,54.67,0,0,1-3-12.67l-.31-2.77a2.61,2.61,0,0,1,2.42-2.91,92.51,92.51,0,0,0,10.83-1.32,1.73,1.73,0,0,0,1.44-1.67A1.65,1.65,0,0,0,47.62,22.28Z"></path>
								<path class="b" d="M31.5,0A31.5,31.5,0,1,0,63,31.5,31.53,31.53,0,0,0,31.5,0Zm0,60A28.5,28.5,0,1,1,60,31.5,28.54,28.54,0,0,1,31.5,60Z"></path>
							</svg>
							{# <img src="{{ file_url('/HTML/assets/images/Accessibility.png') }}" alt="chat-bot"> #}
						</button>
					</div>
					<script type="text/javascript">
						(function (d, t) {
                      var v = d.createElement(t),
                      s = d.getElementsByTagName(t)[0];
                      v.onload = function () {
                      window.voiceflow.chat.load({
                      verify: {
                      projectID: '683488a039a9795b12c8fc2e'
                      },
                      url: 'https://general-runtime.voiceflow.com',
                      versionID: 'production',
                      voice: {
                      url: "https://runtime-api.voiceflow.com"
                      }
                      });
                      }
                      v.src = "https://cdn.voiceflow.com/widget-next/bundle.mjs";
                      v.type = "text/javascript";
                s.parentNode.insertBefore(v, s);
              })(document, 'script');
					</script>
					<script>
						(function () {
						// Activer seulement pour la version arabe / RTL
						const html = document.documentElement;
						const lang = (html.getAttribute('lang') || '').toLowerCase();
						const dir  = (html.getAttribute('dir')  || '').toLowerCase();
						const isAR = dir === 'rtl' || lang === 'ar' || lang.startsWith('ar-');
						if (!isAR) return; // ne rien faire hors AR

						const HOST_SELECTOR = '#voiceflow-chat';
						const LEFT_PX = 20;

						function applyLeft(root) {
							// Déplace à gauche tout élément positionné à droite (ex: launcher + panneau)
							const nodes = root.querySelectorAll('._1wkq7nfd, ._1wkq7nf5, [style*="right"]');
							nodes.forEach(el => {
							const cs = getComputedStyle(el);
							if (cs.position === 'fixed' || cs.position === 'absolute') {
								el.style.left = LEFT_PX + 'px';
								el.style.right = 'auto';
							}
							});
						}

						function boot() {
							const host = document.querySelector(HOST_SELECTOR);
							if (!host || !host.shadowRoot) return false;

							const root = host.shadowRoot;
							applyLeft(root);

							// Ré-appliquer si le widget re-render
							const obs = new MutationObserver(() => applyLeft(root));
							obs.observe(root, {
							subtree: true,
							childList: true,
							attributes: true,
							attributeFilter: ['style', 'class']
							});
							return true;
						}

						// Attente légère jusqu’à ce que le widget soit prêt
						if (!boot()) {
							const t = setInterval(() => { if (boot()) clearInterval(t); }, 100);
						}
						})();
					</script>

					<js-bottom-placeholder token="{{ placeholder_token }}"></js-bottom-placeholder>
				</body>
			</html>
