<?php

/**
 * @file
 * Contains \Drupal\block_plus\Plugin\Block.
 */

namespace Drupal\block_plus\Plugin\Block;

use Drupal\Core\Block\BlockBase;
use Drupal\taxonomy\Entity\Term;

/**
 *
 * @Block(
 *   id = "region_map",
 *   admin_label = @Translation("Block plus: Region map"),
 *   category = @Translation("Block Plus")
 * )
 */
class RegionMap extends BlockBase
{



  public function build()
  {

    $term = \Drupal::routeMatch()->getParameter('taxonomy_term');

    $tid = null;
    if ($term instanceof Term) {
      $tid = $term->id();
    }

    $lang = \Drupal::languageManager()->getCurrentLanguage()->getId();

    $vid = 'region';
    $terms = \Drupal::entityTypeManager()
      ->getStorage('taxonomy_term')
      ->loadByProperties(['vid' => $vid]);

    foreach ($terms as $id => $term) {
      if ($term->hasTranslation($lang)) {
        $term = $term->getTranslation($lang);
      }
      $terms[$id] = $term->label();
    }

    return [
      '#theme' => 'region_map',
      '#data' => [
        'terms' => $terms,
        'active' => $tid,
      ],
      '#attached' => [
        'library' => [
          'block_plus/region-map',
        ],
      ],

    ];
  }

  public function getCacheMaxAge()
  {
    return 0;
  }
}
