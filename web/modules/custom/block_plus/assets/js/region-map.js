(function ($) {



  // Show tooltip on mouseenter over an SVG path
  $('.region-maps #carteContainer .highcharts-point').on('mouseenter', function (event) {
    const tooltipText = $(this).attr('data_tooltip'); // Get the tooltip text from data_tooltip attribute

    // Set the tooltip content and make it visible
    $('#tooltip').text(tooltipText).css({
      'left': event.pageX + 10 + 'px', // Position tooltip near the mouse pointer
      'top': event.pageY + 10 + 'px',
      'display': 'block'
    });
  });

  // Hide the tooltip when mouse leaves the SVG path
  $('.region-maps #carteContainer .highcharts-point').on('mouseleave', function () {
    $('#tooltip').hide();
  });

  // Optional: Update tooltip position while mouse moves
  $('.region-maps #carteContainer .highcharts-point').on('mousemove', function (event) {
    $('#tooltip').css({
      'left': event.pageX + 10 + 'px', // Update the position of the tooltip as the mouse moves
      'top': event.pageY + 10 + 'px'
    });
  });

  // $('.region-maps #carteContainer .highcharts-point').on('click', function () {
  //   const id = $(this).attr('id');
  //   if (!id) return;

  //   window.location.href = `/taxonomy/term/${id}`
  // });
  $('.region-maps #carteContainer .highcharts-point').on('click', function () {
    const id = $(this).attr('id');
    if (!id) return;
    $('.region-maps #carteContainer .highcharts-point').removeClass('active');
    $(this).addClass('active');
    const path = window.location.pathname;
    const isFR = path.startsWith('/fr');
    const isAR = path.startsWith('/ar');
    let langPrefix = '';
    if (isFR) langPrefix = '/fr';
    else if (isAR) langPrefix = '/ar';
     setTimeout(() => {
      window.location.href = `${langPrefix}/taxonomy/term/${id}`;
    }, 200);
  });
})(jQuery);
