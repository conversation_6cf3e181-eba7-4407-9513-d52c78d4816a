<?php
 
namespace Drupal\hierarchical_filters\Controller;
 
use <PERSON><PERSON><PERSON>\Core\Controller\ControllerBase;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Drupal\taxonomy\Entity\Term;
 
class HierarchicalFiltersController extends ControllerBase {
 
  /**
   * Récupère les domaines d'activité pour un secteur donné.
   *
   * @param int $secteur_id
   *   L'ID du terme de taxonomie secteur.
   * @param \Symfony\Component\HttpFoundation\Request $request
   *   L'objet Request.
   *
   * @return \Symfony\Component\HttpFoundation\JsonResponse
   *   La réponse JSON contenant les domaines.
   */
  public function getDomainesBySecteur($secteur_id, Request $request = null) {
    $logger = \Drupal::logger('hierarchical_filters');

    try {
      $response_data = [];

      // Déterminer le vocabulaire à utiliser selon le referer ou un paramètre
      $vocabulary_id = 'domaines_d_activites'; // Par défaut pour reglementation

      // Vérifier si la requête vient de procedure_formulaire
      if ($request) {
        $referer = $request->headers->get('referer');
        $view_type = $request->query->get('view_type');

        if ($view_type === 'procedure_formulaire' ||
            ($referer && strpos($referer, 'procedure-formulaire') !== false)) {
          $vocabulary_id = 'domaines_activite_pf';
        }
      }

      $logger->info('DEBUG - Paramètres reçus: secteur_id=@secteur_id, vocabulary_id=@vocab, view_type=@view_type', [
        '@secteur_id' => $secteur_id,
        '@vocab' => $vocabulary_id,
        '@view_type' => $request ? $request->query->get('view_type') : 'null'
      ]);

      // Récupérer les domaines liés au secteur
      $query = \Drupal::entityQuery('taxonomy_term')
        ->accessCheck(TRUE)
        ->condition('vid', $vocabulary_id)
        ->condition('field_secteur', $secteur_id)
        ->sort('name', 'ASC');

      $term_ids = $query->execute();

      if (!empty($term_ids)) {
        $terms = \Drupal::entityTypeManager()->getStorage('taxonomy_term')->loadMultiple($term_ids);

        foreach ($terms as $term) {
          $response_data[] = [
            'id' => $term->id(),
            'name' => $term->label(),
          ];
        }
      }

      $logger->info('Domaines récupérés pour secteur @secteur_id avec vocabulaire @vocab: @count résultats', [
        '@secteur_id' => $secteur_id,
        '@vocab' => $vocabulary_id,
        '@count' => count($response_data)
      ]);

      return new JsonResponse($response_data);

    } catch (\Exception $e) {
      $logger->error('Erreur lors de la récupération des domaines: @error', [
        '@error' => $e->getMessage()
      ]);

      return new JsonResponse([
        'error' => 'Erreur lors de la récupération des domaines',
        'message' => $e->getMessage()
      ], 500);
    }
  }

  /**
   * Alternative method name for backward compatibility.
   * Alias for getDomainesBySecteur.
   */
  public function getDomains($secteur_id, Request $request = null) {
    return $this->getDomainesBySecteur($secteur_id, $request);
  }
}