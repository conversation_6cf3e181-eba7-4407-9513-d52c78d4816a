/* Hierarchical Filters Styles */

/* Initial hiding of domaine fields until secteur is selected */
form[id*="views-exposed-form-procedure-formulaire"] fieldset[id*="field-domaines-activite-pf"],
form[id*="views-exposed-form-procedure-formulaire"] .form-item-field-domaines-activite-pf-target-id {
  display: none !important;
}

form[id*="views-exposed-form-reglementation"] fieldset[id*="field-domaine-d-activite"],
form[id*="views-exposed-form-reglementation"] .form-item-field-domaine-d-activite-target-id {
  display: none !important;
}

/* Show domaine fields when they have the visible class */
form[id*="views-exposed-form-procedure-formulaire"] fieldset[id*="field-domaines-activite-pf"].hierarchical-filters-visible,
form[id*="views-exposed-form-procedure-formulaire"] .form-item-field-domaines-activite-pf-target-id.hierarchical-filters-visible {
  display: block !important;
}

form[id*="views-exposed-form-reglementation"] fieldset[id*="field-domaine-d-activite"].hierarchical-filters-visible,
form[id*="views-exposed-form-reglementation"] .form-item-field-domaine-d-activite-target-id.hierarchical-filters-visible {
  display: block !important;
}

/* Ensure dynamically created radio buttons are visible */
form[id*="views-exposed-form-procedure-formulaire"] fieldset[id*="field-domaines-activite-pf"].hierarchical-filters-visible .form-item,
form[id*="views-exposed-form-procedure-formulaire"] fieldset[id*="field-domaines-activite-pf"].hierarchical-filters-visible .form-type-radio {
  display: block !important;
}

form[id*="views-exposed-form-reglementation"] fieldset[id*="field-domaine-d-activite"].hierarchical-filters-visible .form-item,
form[id*="views-exposed-form-reglementation"] fieldset[id*="field-domaine-d-activite"].hierarchical-filters-visible .form-type-radio {
  display: block !important;
}

/* Loading state for domaine container */
.hierarchical-filters-loading {
  position: relative;
  opacity: 0.6;
  pointer-events: none;
}

.hierarchical-filters-loading::after {
  content: "Chargement des domaines...";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.9);
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  color: #666;
  border: 1px solid #ddd;
  z-index: 10;
}

/* Ensure hidden domaines are completely invisible */
.hierarchical-filters-hidden {
  display: none !important;
  visibility: hidden !important;
}

/* Smooth transitions */
.form-checkboxes .form-item {
  transition: opacity 0.2s ease-in-out;
}

/* Disabled state styling */
.hierarchical-filters-loading input[type="checkbox"] {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Container styling for better UX */
.domaine-radio-container {
  min-height: 40px;
  transition: all 0.3s ease-in-out;
}

/* Loading animation */
@keyframes hierarchical-loading {
  0% { opacity: 0.6; }
  50% { opacity: 0.8; }
  100% { opacity: 0.6; }
}

.hierarchical-filters-loading {
  animation: hierarchical-loading 1.5s ease-in-out infinite;
} 