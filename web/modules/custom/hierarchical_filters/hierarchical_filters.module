<?php

/**
 * @file
 * Module pour les filtres hiérarchiques avec boutons radio.
 */

use Drupal\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\taxonomy\Entity\Term;
use <PERSON><PERSON>al\Core\Url;
use <PERSON><PERSON>al\Core\Ajax\AjaxResponse;
use <PERSON><PERSON>al\Core\Ajax\ReplaceCommand;

/**
 * Helper function to check if a view is targeted by this module.
 */
function _hierarchical_filters_is_targeted_view($view_id) {
  $targeted_views = ['reglementation', 'procedure_formulaire'];
  return in_array($view_id, $targeted_views);
}

/**
 * Implements hook_theme_suggestions_HOOK_alter().
 */
function hierarchical_filters_theme_suggestions_views_view_filters_alter(array &$suggestions, array $variables) {
  // Vérifier si la vue existe et a un ID
  if (isset($variables['view']) && is_object($variables['view']) && method_exists($variables['view'], 'id')) {
    $view = $variables['view'];
    $view_id = $view->id();
    if (_hierarchical_filters_is_targeted_view($view_id)) {
      $suggestions[] = 'views_view_filters__' . $view_id;
    }
  }
}

/**
 * Implements hook_preprocess_views_exposed_form().
 */
function hierarchical_filters_preprocess_views_exposed_form(&$variables) {
  // Vérifier si c'est une vue ciblée
  if (isset($variables['view']) && is_object($variables['view']) && method_exists($variables['view'], 'id')) {
    $view = $variables['view'];
    $view_id = $view->id();
    
    if (_hierarchical_filters_is_targeted_view($view_id)) {
      $logger = \Drupal::logger('hierarchical_filters');
      $logger->info('Traitement de la vue @view_id - Display: @display', [
        '@view_id' => $view_id,
        '@display' => $view->current_display
      ]);
      
      // Vérifier si le formulaire contient les champs nécessaires
      $has_secteur = !empty($variables['form']['field_secteur_target_id']);

      // Support pour différents champs domaine selon la vue
      $has_domaine = !empty($variables['form']['field_domaine_d_activite_target_id']) ||
                     !empty($variables['form']['field_domaines_activite_pf_target_id']);
      
      if ($has_secteur && $has_domaine) {
        // Ajouter notre bibliothèque JavaScript
        $variables['#attached']['library'][] = 'hierarchical_filters/hierarchical_filters';
        
        // Ajouter des paramètres de débogage
        $variables['#attached']['drupalSettings']['hierarchical_filters'] = [
          'debug' => TRUE,
          'view_id' => $view_id,
          'display_id' => $view->current_display,
          'ajax_url' => '/domaines-by-secteur/',
        ];
        
        $logger->info('Bibliothèque JS attachée pour @view_id - Secteur: @secteur, Domaine: @domaine', [
          '@view_id' => $view_id,
          '@secteur' => $has_secteur ? 'oui' : 'non',
          '@domaine' => $has_domaine ? 'oui' : 'non'
        ]);
      } else {
        $logger->warning('Champs manquants pour @view_id - Secteur: @secteur, Domaine: @domaine', [
          '@view_id' => $view_id,
          '@secteur' => $has_secteur ? 'oui' : 'non',
          '@domaine' => $has_domaine ? 'oui' : 'non'
        ]);
      }
    }
  }
}

/**
 * Implements hook_form_views_exposed_form_alter().
 */
function hierarchical_filters_form_views_exposed_form_alter(&$form, FormStateInterface $form_state, $form_id) {
  // Cibler spécifiquement les formulaires exposés des vues ciblées
  $targeted_forms = [
    'views-exposed-form-reglementation',
    'views-exposed-form-procedure-formulaire'
  ];
  
  $is_targeted_form = false;
  foreach ($targeted_forms as $targeted_form) {
    if (isset($form['#id']) && strpos($form['#id'], $targeted_form) === 0) {
      $is_targeted_form = true;
      break;
    }
  }
  
  if ($is_targeted_form) {
    $logger = \Drupal::logger('hierarchical_filters');
    $logger->info('Modification du formulaire exposé: @form_id', [
      '@form_id' => $form['#id'],
    ]);
    
    // Vérifier si les champs existent
    $has_secteur = isset($form['field_secteur_target_id']);

    // Support pour différents champs domaine selon la vue
    $has_domaine = isset($form['field_domaine_d_activite_target_id']) ||
                   isset($form['field_domaines_activite_pf_target_id']);
    
    if ($has_secteur && $has_domaine) {
      // Ajouter la bibliothèque JavaScript
      $form['#attached']['library'][] = 'hierarchical_filters/hierarchical_filters';
      $form['#attached']['drupalSettings']['hierarchical_filters'] = [
        'debug' => TRUE,
        'form_id' => $form['#id'],
        'ajax_url' => '/domaines-by-secteur/',
      ];
      
      // Optionnel: masquer le champ domaine initialement s'il n'y a pas de secteur sélectionné
      $current_secteur = $form['field_secteur_target_id']['#default_value'] ?? '';
      if (empty($current_secteur) || $current_secteur === 'All') {
        // Le JavaScript se chargera de gérer l'état initial
      }
      
      $logger->info('Configuration appliquée au formulaire @form_id', [
        '@form_id' => $form['#id']
      ]);
    } else {
      $logger->warning('Champs manquants dans le formulaire @form_id - Secteur: @secteur, Domaine: @domaine', [
        '@form_id' => $form['#id'],
        '@secteur' => $has_secteur ? 'oui' : 'non',
        '@domaine' => $has_domaine ? 'oui' : 'non'
      ]);
    }
  }
}

/**
 * Récupère les domaines d'activités pour un secteur donné.
 * Cette fonction est utilisée par le contrôleur AJAX.
 */
function hierarchical_filters_get_domains_for_secteur($secteur_id) {
  $domains = [];
  
  try {
    // Récupérer directement les termes de taxonomie des domaines d'activité
    $query = \Drupal::entityQuery('taxonomy_term')
      ->accessCheck(TRUE)
      ->condition('vid', 'domaines_d_activites')
      ->condition('field_secteur', $secteur_id)
      ->sort('name', 'ASC');

    $term_ids = $query->execute();

    if (!empty($term_ids)) {
      $terms = \Drupal::entityTypeManager()->getStorage('taxonomy_term')->loadMultiple($term_ids);

      foreach ($terms as $term) {
        $domains[$term->id()] = $term->label();
      }
    }
  } catch (\Exception $e) {
    \Drupal::logger('hierarchical_filters')->error('Erreur lors de la récupération des domaines: @error', [
      '@error' => $e->getMessage(),
    ]);
  }
  
  return $domains;
}

/**
 * Implements hook_views_pre_view().
 */
function hierarchical_filters_views_pre_view($view, $display_id, &$args) {
  if (_hierarchical_filters_is_targeted_view($view->id())) {
    $input = $view->getExposedInput();
    
    // Journaliser les paramètres d'entrée pour le débogage
    \Drupal::logger('hierarchical_filters')->debug('Paramètres exposés reçus pour @view/@display: @input', [
      '@view' => $view->id(),
      '@display' => $display_id,
      '@input' => print_r($input, TRUE)
    ]);
  }
}

/**
 * Implements hook_form_alter().
 */
function hierarchical_filters_form_alter(&$form, FormStateInterface $form_state, $form_id) {
  // Attach the library to Views exposed forms for targeted views
  $targeted_forms = [
    'views-exposed-form-reglementation',
    'views-exposed-form-procedure-formulaire'
  ];
  
  foreach ($targeted_forms as $targeted_form) {
    if (strpos($form_id, $targeted_form) === 0) {
      $form['#attached']['library'][] = 'hierarchical_filters/hierarchical_filters';
      $form['#attributes']['data-once'] = 'hierarchical-filters';
      break;
    }
  }
} 