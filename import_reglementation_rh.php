<?php

use <PERSON><PERSON><PERSON>\node\Entity\Node;
use Drupal\taxonomy\Entity\Term;
use Drupal\file\Entity\File;
use Drupal\Core\File\FileSystemInterface;

/**
 * Script d'importation des réglementations RH depuis un fichier CSV
 * Usage: drush php:script import_reglementation_rh.php
 */

// Chemin vers le fichier CSV
$csv_file_path = '/var/www/mtl/Canvas Réglementation - RH.csv';

// Vérifier si le fichier existe
if (!file_exists($csv_file_path)) {
  echo "Erreur: Le fichier CSV n'existe pas: $csv_file_path\n";
  return;
}

echo "Début de l'importation des réglementations RH...\n";

/**
 * Fonction pour récupérer ou créer un terme de taxonomie par son nom
 */
function getTaxonomyTermByName($name, $vocabulary) {
  $terms = \Drupal::entityTypeManager()
    ->getStorage('taxonomy_term')
    ->loadByProperties([
      'name' => $name,
      'vid' => $vocabulary,
    ]);
  
  if ($terms) {
    return reset($terms);
  }
  
  // Créer le terme s'il n'existe pas
  echo "Création du terme '$name' dans le vocabulaire '$vocabulary'\n";
  $term = Term::create([
    'vid' => $vocabulary,
    'name' => $name,
    'langcode' => 'fr',
  ]);
  $term->save();
  
  return $term;
}

/**
 * Fonction pour parser la date du CSV
 */
function parseDate($date_string) {
  if (empty($date_string)) {
    return null;
  }
  
  // Format attendu: dd/mm/yyyy
  $date_parts = explode('/', $date_string);
  if (count($date_parts) == 3) {
    $day = str_pad($date_parts[0], 2, '0', STR_PAD_LEFT);
    $month = str_pad($date_parts[1], 2, '0', STR_PAD_LEFT);
    $year = $date_parts[2];
    return "$year-$month-$day";
  }
  
  return null;
}

/**
 * Fonction pour créer ou récupérer un fichier
 */
function createOrGetFile($filename, $pdf_directory = 'web/modules/custom/import_reglementation/pdf/') {
  if (empty($filename)) {
    return null;
  }
  
  $file_path = $pdf_directory . $filename;
  
  // Vérifier si le fichier existe physiquement
  if (!file_exists($file_path)) {
    // Ignorer silencieusement les fichiers manquants
    return null;
  }
  
  // Rechercher le fichier existant dans Drupal
  $files = \Drupal::entityTypeManager()
    ->getStorage('file')
    ->loadByProperties(['filename' => $filename]);
  
  if ($files) {
    return reset($files);
  }
  
  // Créer une nouvelle entité fichier
  $file_system = \Drupal::service('file_system');
  $destination = 'public://reglementation/pdf/' . $filename;
  
  // Créer le répertoire de destination s'il n'existe pas
  $directory = dirname($destination);
  $file_system->prepareDirectory($directory, FileSystemInterface::CREATE_DIRECTORY | FileSystemInterface::MODIFY_PERMISSIONS);
  
  // Copier le fichier
  $uri = $file_system->copy($file_path, $destination, FileSystemInterface::EXISTS_REPLACE);
  
  if ($uri) {
    $file = File::create([
      'filename' => $filename,
      'uri' => $uri,
      'status' => 1,
    ]);
    $file->save();
    echo "Fichier créé: $filename\n";
    return $file;
  }
  
  return null;
}

/**
 * Fonction pour vérifier si un contenu existe déjà
 */
function getExistingNode($title, $numero) {
  $query = \Drupal::entityQuery('node')
    ->condition('type', 'reglementation')
    ->condition('title', $title)
    ->accessCheck(FALSE);
  
  $nids = $query->execute();
  
  if ($nids) {
    return Node::load(reset($nids));
  }
  
  // Recherche alternative par numéro si disponible
  if (!empty($numero)) {
    $query = \Drupal::entityQuery('node')
      ->condition('type', 'reglementation')
      ->condition('title', '%' . $numero . '%', 'LIKE')
      ->accessCheck(FALSE);
    
    $nids = $query->execute();
    
    if ($nids) {
      return Node::load(reset($nids));
    }
  }
  
  return null;
}

// Récupérer les termes existants
$rh_sector = getTaxonomyTermByName('RH', 'modes_de_transport');
$texte_general_domaine = getTaxonomyTermByName('Texte général', 'domaines_d_activites');

if (!$rh_sector) {
  echo "Erreur: Le terme 'RH' n'existe pas dans le vocabulaire 'modes_de_transport'\n";
  return;
}

if (!$texte_general_domaine) {
  echo "Erreur: Le terme 'Texte général' n'existe pas dans le vocabulaire 'domaines_d_activites'\n";
  return;
}

// Lire et parser le fichier CSV
$handle = fopen($csv_file_path, 'r');
if (!$handle) {
  echo "Erreur: Impossible d'ouvrir le fichier CSV\n";
  return;
}

// Ignorer les lignes d'en-têtes et d'exemples (lignes 1-17)
$line_number = 0;
while (($data = fgetcsv($handle, 0, ',', '"')) !== FALSE && $line_number < 17) {
  $line_number++;
}

$imported = 0;
$updated = 0;
$errors = 0;

// Traiter chaque ligne de données
while (($data = fgetcsv($handle, 0, ',', '"')) !== FALSE) {
  $line_number++;
  
  // Vérifier que la ligne contient des données valides
  if (empty($data[5]) || empty($data[6])) { // Intitulé FR et AR
    continue;
  }
  
  try {
    // Extraire les données
    $type_fr = trim($data[0]);
    $type_ar = trim($data[1]);
    $domaine_fr = trim($data[2]);
    $domaine_ar = trim($data[3]);
    $numero = trim($data[4]);
    $titre_fr = trim($data[5]);
    $titre_ar = trim($data[6]);
    $date_publication = trim($data[7]);
    $pdf_fr = trim($data[8]);
    $pdf_ar = trim($data[9]);
    $remarques = trim($data[10]);
    
    // Récupérer ou créer le domaine d'activité depuis les données CSV
    $domaine_term = null;
    if (!empty($domaine_fr)) {
      $domaine_term = getTaxonomyTermByName($domaine_fr, 'domaines_d_activites');
      
      // Ajouter le secteur au domaine d'activité
      if ($domaine_term && $domaine_term->hasField('field_secteur')) {
        $current_secteur = $domaine_term->get('field_secteur')->target_id;
        if (!$current_secteur || $current_secteur != $rh_sector->id()) {
          $domaine_term->set('field_secteur', ['target_id' => $rh_sector->id()]);
          $domaine_term->save();
          echo "Secteur RH assigné au domaine: $domaine_fr\n";
        }
      }
      
      // Ajouter la traduction arabe si disponible
      if (!empty($domaine_ar) && $domaine_term && !$domaine_term->hasTranslation('ar')) {
        $domaine_term->addTranslation('ar', ['name' => $domaine_ar]);
        $domaine_term->save();
        echo "Traduction arabe ajoutée pour le domaine: $domaine_fr\n";
      }
    }
    
    echo "\nTraitement ligne $line_number: $titre_fr\n";
    
    // Vérifier si le contenu existe déjà
    $existing_node = getExistingNode($titre_fr, $numero);
    
    // Utiliser directement le type français du CSV
    $type_name = $type_fr;
    
    // Récupérer ou créer le terme de type de loi
    $type_term = getTaxonomyTermByName($type_name, 'type');
    
    // Ajouter la traduction arabe du type si disponible
    if (!empty($type_ar) && $type_term && !$type_term->hasTranslation('ar')) {
      $type_term->addTranslation('ar', ['name' => $type_ar]);
      $type_term->save();
      echo "Traduction arabe ajoutée pour le type: $type_name\n";
    }
    
    // Parser la date
    $date_formatted = parseDate($date_publication);
    
    // Gérer les fichiers PDF
    $pdf_fr_file = createOrGetFile($pdf_fr);
    $pdf_ar_file = createOrGetFile($pdf_ar);
    
    // Gérer le titre long (limite de 255 caractères pour le champ title)
    $title_for_node = $titre_fr;
    $long_title = null;
    
    if (mb_strlen($titre_fr, 'UTF-8') > 255) {
      $title_for_node = mb_substr($titre_fr, 0, 250, 'UTF-8') . '...';
      $long_title = $titre_fr;
      echo "Titre tronqué pour: $title_for_node\n";
    }
    
    // Préparer les données du nœud
    $node_data = [
      'type' => 'reglementation',
      'title' => $title_for_node,
      'field_type_loi' => ['target_id' => $type_term->id()],
      'field_secteur' => ['target_id' => $rh_sector->id()],
      'field_domaine_d_activite' => ['target_id' => $domaine_term ? $domaine_term->id() : $texte_general_domaine->id()],
      'status' => 1,
      'uid' => 1,
    ];
    
    // Ajouter le titre long si nécessaire
    if ($long_title) {
      $node_data['field_titre_long'] = ['value' => $long_title];
    }
    
    // Ajouter la date si disponible
    if ($date_formatted) {
      $node_data['field_date'] = ['value' => $date_formatted];
    }
    
    // Ajouter le fichier PDF (prioriser le français, sinon l'arabe)
    if ($pdf_fr_file) {
      $node_data['field_lien_telechargement'] = ['target_id' => $pdf_fr_file->id()];
    } elseif ($pdf_ar_file) {
      $node_data['field_lien_telechargement'] = ['target_id' => $pdf_ar_file->id()];
    }
    
    if ($existing_node) {
      // Mettre à jour le nœud existant
      foreach ($node_data as $field => $value) {
        if ($field !== 'type') {
          $existing_node->set($field, $value);
        }
      }
      $existing_node->save();
      
      // Ajouter/mettre à jour la traduction arabe
      if (!empty($titre_ar)) {
        // Gérer aussi le titre arabe long
        $title_ar_for_node = $titre_ar;
        if (mb_strlen($titre_ar, 'UTF-8') > 255) {
          $title_ar_for_node = mb_substr($titre_ar, 0, 250, 'UTF-8') . '...';
        }
        
        if ($existing_node->hasTranslation('ar')) {
          $ar_translation = $existing_node->getTranslation('ar');
          $ar_translation->setTitle($title_ar_for_node);
          if (mb_strlen($titre_ar, 'UTF-8') > 255 && $ar_translation->hasField('field_titre_long')) {
            $ar_translation->set('field_titre_long', ['value' => $titre_ar]);
          }
          $ar_translation->save();
        } else {
          $ar_data = ['title' => $title_ar_for_node];
          if (mb_strlen($titre_ar, 'UTF-8') > 255) {
            $ar_data['field_titre_long'] = ['value' => $titre_ar];
          }
          $existing_node->addTranslation('ar', $ar_data);
          $existing_node->save();
        }
      }
      
      echo "Nœud mis à jour: {$existing_node->id()}\n";
      $updated++;
    } else {
      // Créer un nouveau nœud
      $node = Node::create($node_data);
      $node->save();
      
      // Ajouter la traduction arabe
      if (!empty($titre_ar)) {
        // Gérer aussi le titre arabe long pour les nouveaux nœuds
        $title_ar_for_node = $titre_ar;
        $ar_data = ['title' => $title_ar_for_node];
        
        if (mb_strlen($titre_ar, 'UTF-8') > 255) {
          $title_ar_for_node = mb_substr($titre_ar, 0, 250, 'UTF-8') . '...';
          $ar_data['title'] = $title_ar_for_node;
          $ar_data['field_titre_long'] = ['value' => $titre_ar];
        }
        
        $node->addTranslation('ar', $ar_data);
        $node->save();
      }
      
      echo "Nouveau nœud créé: {$node->id()}\n";
      $imported++;
    }
    
  } catch (Exception $e) {
    echo "Erreur ligne $line_number: " . $e->getMessage() . "\n";
    $errors++;
  }
}

fclose($handle);

echo "\n=== RÉSULTATS DE L'IMPORTATION ===\n";
echo "Nouveaux contenus créés: $imported\n";
echo "Contenus mis à jour: $updated\n";
echo "Erreurs: $errors\n";
echo "Total traité: " . ($imported + $updated) . "\n";
echo "Importation terminée!\n";